import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage  } from '@tess-f/backend-utils'
import { EventType } from '@tess-f/shared-config'
import submitAssessment from '../../../services/internal/submit-assessment-service.js'
import { SubmitAssessmentRequest, questionResponseSchema } from '../../../models/internal/submit-assessment.js'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z, ZodError } from 'zod'
import httpStatus from 'http-status'
import getEvaluationSession from '../../../services/mssql/session/get.service.js'
import updateEvaluationSession from '../../../services/mssql/session/update.service.js'
import { SessionModel } from '../../../models/session.model.js'

const {  INTERNAL_SERVER_ERROR, NO_CONTENT, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.Submit-Assessment', httpLogTransformer)

/**
 * HTTP controller for submitting completed assessments.
 * 
 * Handles POST
 * Expected request body:
 * - sessionId: uuid found in request path
 * - responses: QuestionResponse[] - Array of student responses
 * 
 */
export default async function submitAssessmentController(
  req: Request,
  res: Response
): Promise<void> {
  try {
    const sessionId = z.object({ id: zodGUID }).parse(req.params)
    const session = await getEvaluationSession(sessionId.id)
    const endTime = new Date()

    const userResponses = z.array(questionResponseSchema).parse(req.body)
    
    log('info', 'Received assessment submission request', {
      request: req,
      sessionId:sessionId.id,
      responsesCount: userResponses?.length || 0,
      eventType: EventType.evaluation_grade
    })

    // Extract request data
    const submissionRequest: SubmitAssessmentRequest = {
      sessionId: sessionId.id,
      responses: userResponses,
      startTime: session.fields.Start!.toISOString(),
      endTime: endTime.toISOString()
      
    }

    const sessionUpdate: SessionModel = new SessionModel({
      Id: sessionId.id,
      End: endTime,
      Score: 0,
      Passed: false
    })

    // need to save session 
    await updateEvaluationSession(sessionUpdate)

    // Return success with just a ok status 
    res.sendStatus(NO_CONTENT)

    // Process the submission
    const result = await submitAssessment(submissionRequest)

    log('info', 'Assessment submission completed successfully', {
      sessionId: result.sessionId,
      passed: result.passed,
      totalScore: result.totalScore,
      maxScore: result.maxScore,
      responsesCount: result.responsesCount,
      eventType: EventType.evaluation_create
    })

  } catch (error) {
    const errorMessage = getErrorMessage(error)
    
    log('error', 'Failed to submit assessment', {
      sessionId: req.body?.sessionId,
      error: errorMessage,
      eventType: EventType.evaluation_create
    })
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', {
        errorMessage: zodErrorToMessage(error),
        success: false,
        sessionId: req.params.id,
        req,
        eventType: EventType.input_validation_errors
      })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to submit session data', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
